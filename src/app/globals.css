@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Card Grid Layout */
.card-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .card-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Card Items */
.card-item {
  position: relative;
}

.card-large {
  grid-column: span 1;
}

.card-medium {
  grid-column: span 1;
}

.card-wide {
  grid-column: span 1;
}

.card-small {
  grid-column: span 1;
}

@media (min-width: 768px) {
  .card-large {
    grid-column: span 2;
  }

  .card-medium {
    grid-column: span 1;
  }

  .card-wide {
    grid-column: span 2;
  }

  .card-small {
    grid-column: span 1;
  }
}

@media (min-width: 1024px) {
  .card-large {
    grid-column: span 2;
  }

  .card-medium {
    grid-column: span 1;
  }

  .card-wide {
    grid-column: span 2;
  }

  .card-small {
    grid-column: span 1;
  }
}

/* Card Styling */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(32px) saturate(200%);
  -webkit-backdrop-filter: blur(32px) saturate(200%);
  border-radius: 1.5rem;
  padding: 2rem;
  min-height: 280px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.glass-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
  border-color: rgba(255, 255, 255, 0.3);
}

.glass-card.large {
  min-height: 400px;
}

.glass-card.small {
  min-height: 240px;
}

@media (max-width: 767px) {
  .glass-card {
    padding: 1.5rem;
    min-height: 240px;
  }

  .glass-card.large {
    min-height: 320px;
  }
}
