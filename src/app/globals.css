@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Bento Grid Layout */
.bento-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  grid-auto-rows: minmax(200px, auto);
  grid-auto-flow: dense;
}

@media (min-width: 768px) {
  .bento-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .bento-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* Bento Grid Items */
.bento-item {
  position: relative;
  min-height: 200px;
}

.bento-large {
  grid-column: span 1;
  grid-row: span 2;
}

.bento-medium {
  grid-column: span 1;
  grid-row: span 2;
}

.bento-wide {
  grid-column: span 1;
  grid-row: span 1;
}

.bento-small {
  grid-column: span 1;
  grid-row: span 1;
}

@media (min-width: 768px) {
  .bento-large {
    grid-column: span 3;
    grid-row: span 2;
  }

  .bento-medium {
    grid-column: span 2;
    grid-row: span 2;
  }

  .bento-wide {
    grid-column: span 2;
    grid-row: span 1;
  }

  .bento-small {
    grid-column: span 1;
    grid-row: span 1;
  }
}

@media (min-width: 1024px) {
  .bento-large {
    grid-column: span 4;
    grid-row: span 2;
  }

  .bento-medium {
    grid-column: span 2;
    grid-row: span 2;
  }

  .bento-wide {
    grid-column: span 3;
    grid-row: span 1;
  }

  .bento-small {
    grid-column: span 2;
    grid-row: span 1;
  }
}

/* Bento Card Styling */
.bento-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(32px) saturate(200%);
  -webkit-backdrop-filter: blur(32px) saturate(200%);
  border-radius: 1.5rem;
  padding: 1.5rem;
  height: 100%;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.bento-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
  border-color: rgba(255, 255, 255, 0.3);
}

@media (min-width: 768px) {
  .bento-card {
    padding: 2rem;
  }
}