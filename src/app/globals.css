@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient 25s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Card Grid Layout - 流式布局 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  align-items: start;
}

@media (min-width: 768px) {
  .card-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .card-grid {
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  }
}

/* Card Items */
.card-item {
  position: relative;
  width: 100%;
}

/* 大卡片在较大屏幕上占据更多空间 */
.card-large {
  grid-column: span 1;
}

@media (min-width: 768px) {
  .card-large {
    grid-column: span 2;
  }
}

@media (min-width: 1200px) {
  .card-large {
    grid-column: span 2;
  }
}

/* 宽卡片在中等屏幕以上占据两列 */
.card-wide {
  grid-column: span 1;
}

@media (min-width: 768px) {
  .card-wide {
    grid-column: span 2;
  }
}

/* 中等和小卡片保持单列 */
.card-medium,
.card-small {
  grid-column: span 1;
}

/* Card Styling */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(32px) saturate(200%);
  -webkit-backdrop-filter: blur(32px) saturate(200%);
  border-radius: 1.5rem;
  padding: 2rem;
  min-height: 280px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.glass-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
  border-color: rgba(255, 255, 255, 0.3);
}

.glass-card.large {
  min-height: 400px;
}

.glass-card.small {
  min-height: 240px;
}

@media (max-width: 767px) {
  .glass-card {
    padding: 1.5rem;
    min-height: 240px;
  }

  .glass-card.large {
    min-height: 320px;
  }
}

/* Footer Styling */
.footer-glass {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px -8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.footer-glass:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.footer-link {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.footer-link:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.1);
}
